// --- Auth Service (handled by AuthContext now) ---
// These functions are now primarily handled by AuthContext using supabase.auth
// Note: Currently using demo data instead of Supabase for testing

// --- Report Service ---
export const getReports = async (params: any) => {
    // Demo data for testing without Supabase
    const demoData = [
        {
            id: 1,
            report_date: '2024-01-15',
            notes: 'تقرير يومي للمبيعات',
            users: { name: 'أحمد محمد', branches: { name: 'الفرع الرئيسي' } },
            report_items: [
                { quantity: 10, unit_price: 50 },
                { quantity: 5, unit_price: 100 }
            ]
        },
        {
            id: 2,
            report_date: '2024-01-16',
            notes: 'تقرير مبيعات الأمس',
            users: { name: 'فاطمة أحمد', branches: { name: 'فرع الشمال' } },
            report_items: [
                { quantity: 8, unit_price: 75 },
                { quantity: 12, unit_price: 25 }
            ]
        }
    ];

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return { data: demoData };
};

export const createReport = async (reportData: { items: any[]; notes: string; report_date?: string }) => {
    const { items, notes, report_date } = reportData;

    // Demo implementation - simulate creating a report
    const newReport = {
        id: Math.floor(Math.random() * 1000) + 100,
        user_id: 'demo-user',
        notes: notes,
        report_date: report_date || new Date().toISOString().split('T')[0],
    };

    const reportItems = items.map(item => ({
        report_id: newReport.id,
        product_id: item.productId,
        quantity: item.quantity,
        unit_price: item.unitPrice,
    }));

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('تم إنشاء التقرير:', { report: newReport, reportItems });

    return { report: newReport, reportItems };
};

// --- Product Service ---
export const getProducts = async (searchTerm: string) => {
    // Demo products data
    const allProducts = [
        { id: 1, model_name: 'لابتوب ديل XPS 13' },
        { id: 2, model_name: 'لابتوب HP Pavilion' },
        { id: 3, model_name: 'ماوس لوجيتك MX Master' },
        { id: 4, model_name: 'كيبورد ميكانيكي' },
        { id: 5, model_name: 'شاشة سامسونج 24 بوصة' },
        { id: 6, model_name: 'طابعة كانون' },
        { id: 7, model_name: 'سماعات سوني' },
        { id: 8, model_name: 'كاميرا ويب' }
    ];

    // Filter products based on search term
    let filteredProducts = allProducts;
    if (searchTerm) {
        filteredProducts = allProducts.filter(product =>
            product.model_name.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    return { data: filteredProducts };
};