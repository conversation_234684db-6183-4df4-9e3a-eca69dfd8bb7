// API Configuration
const API_BASE_URL = 'http://localhost:3001/api';

// Helper function to get auth token
const getAuthToken = () => {
    return localStorage.getItem('auth_token');
};

// Helper function to make authenticated requests
const makeRequest = async (url: string, options: RequestInit = {}) => {
    const token = getAuthToken();

    const config: RequestInit = {
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...(token && { Authorization: `Bearer ${token}` }),
            ...options.headers,
        },
    };

    const response = await fetch(`${API_BASE_URL}${url}`, config);

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
};

// --- Auth Service ---
export const login = async (email: string, password: string) => {
    const response = await makeRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
    });

    if (response.success) {
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('user_data', JSON.stringify(response.data.user));
    }

    return response;
};

export const logout = async () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
};

export const getCurrentUser = async () => {
    try {
        const response = await makeRequest('/auth/me');
        return response.data.user;
    } catch (error) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        throw error;
    }
};

// --- Report Service ---
export const getReports = async (params: any = {}) => {
    const queryParams = new URLSearchParams();

    if (params.status) queryParams.append('status', params.status);
    if (params.date_from) queryParams.append('date_from', params.date_from);
    if (params.date_to) queryParams.append('date_to', params.date_to);
    if (params.branch_id) queryParams.append('branch_id', params.branch_id);

    const url = `/reports${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await makeRequest(url);

    return { data: response.data.reports };
};

export const getReportById = async (id: string) => {
    const response = await makeRequest(`/reports/${id}`);
    return response.data.report;
};

export const createReport = async (reportData: { items: any[]; notes: string; report_date?: string }) => {
    const { items, notes, report_date } = reportData;

    const payload = {
        report_date: report_date || new Date().toISOString().split('T')[0],
        notes,
        items: items.map(item => ({
            product_id: item.productId,
            quantity: item.quantity,
            unit_price: item.unitPrice,
        })),
    };

    const response = await makeRequest('/reports', {
        method: 'POST',
        body: JSON.stringify(payload),
    });

    return response.data.report;
};

export const updateReportStatus = async (id: string, status: string, review_notes?: string) => {
    const response = await makeRequest(`/reports/${id}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ status, review_notes }),
    });

    return response;
};

export const deleteReport = async (id: string) => {
    const response = await makeRequest(`/reports/${id}`, {
        method: 'DELETE',
    });

    return response;
};

// --- Product Service ---
export const getProducts = async (searchTerm?: string, category?: string) => {
    const queryParams = new URLSearchParams();

    if (searchTerm) queryParams.append('search', searchTerm);
    if (category) queryParams.append('category', category);

    const queryString = queryParams.toString();
    const url = queryString ? `/products?${queryString}` : '/products';
    const response = await makeRequest(url);

    return { data: response.data.products };
};

export const getProductById = async (id: string) => {
    const response = await makeRequest(`/products/${id}`);
    return response.data.product;
};

export const createProduct = async (productData: any) => {
    const response = await makeRequest('/products', {
        method: 'POST',
        body: JSON.stringify(productData),
    });

    return response.data.product;
};

export const updateProduct = async (id: string, productData: any) => {
    const response = await makeRequest(`/products/${id}`, {
        method: 'PUT',
        body: JSON.stringify(productData),
    });

    return response.data.product;
};

export const deleteProduct = async (id: string) => {
    const response = await makeRequest(`/products/${id}`, {
        method: 'DELETE',
    });

    return response;
};

export const getProductCategories = async () => {
    const response = await makeRequest('/products/categories/list');
    return response.data.categories;
};

// --- Branch Service ---
export const getBranches = async () => {
    const response = await makeRequest('/branches');
    return response.data.branches;
};

export const getBranchById = async (id: string) => {
    const response = await makeRequest(`/branches/${id}`);
    return response.data.branch;
};

export const createBranch = async (branchData: any) => {
    const response = await makeRequest('/branches', {
        method: 'POST',
        body: JSON.stringify(branchData),
    });

    return response.data.branch;
};

export const updateBranch = async (id: string, branchData: any) => {
    const response = await makeRequest(`/branches/${id}`, {
        method: 'PUT',
        body: JSON.stringify(branchData),
    });

    return response.data.branch;
};

export const deleteBranch = async (id: string) => {
    const response = await makeRequest(`/branches/${id}`, {
        method: 'DELETE',
    });

    return response;
};

// --- User Service ---
export const getUsers = async () => {
    const response = await makeRequest('/users');
    return response.data.users;
};

export const getUserById = async (id: string) => {
    const response = await makeRequest(`/users/${id}`);
    return response.data.user;
};

export const createUser = async (userData: any) => {
    const response = await makeRequest('/users', {
        method: 'POST',
        body: JSON.stringify(userData),
    });

    return response.data.user;
};

export const updateUser = async (id: string, userData: any) => {
    const response = await makeRequest(`/users/${id}`, {
        method: 'PUT',
        body: JSON.stringify(userData),
    });

    return response.data.user;
};

export const changePassword = async (id: string, passwordData: any) => {
    const response = await makeRequest(`/users/${id}/password`, {
        method: 'PATCH',
        body: JSON.stringify(passwordData),
    });

    return response;
};

export const deleteUser = async (id: string) => {
    const response = await makeRequest(`/users/${id}`, {
        method: 'DELETE',
    });

    return response;
};

// --- Stats Service ---
export const getDashboardStats = async () => {
    const response = await makeRequest('/stats/dashboard');
    return response.data;
};

export const getSalesStats = async (params: any = {}) => {
    const queryParams = new URLSearchParams();

    if (params.period) queryParams.append('period', params.period);
    if (params.start_date) queryParams.append('start_date', params.start_date);
    if (params.end_date) queryParams.append('end_date', params.end_date);
    if (params.branch_id) queryParams.append('branch_id', params.branch_id);

    const queryString = queryParams.toString();
    const url = queryString ? `/stats/sales?${queryString}` : '/stats/sales';
    const response = await makeRequest(url);

    return response.data;
};

export const getProductStats = async (params: any = {}) => {
    const queryParams = new URLSearchParams();

    if (params.category) queryParams.append('category', params.category);
    if (params.limit) queryParams.append('limit', params.limit);

    const queryString = queryParams.toString();
    const url = queryString ? `/stats/products?${queryString}` : '/stats/products';
    const response = await makeRequest(url);

    return response.data;
};