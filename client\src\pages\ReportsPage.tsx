import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';

const ReportsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { formatCurrency } = useCurrency();
  const [reports, setReports] = useState<any[]>([]);
  const [filteredReports, setFilteredReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [branchFilter, setBranchFilter] = useState('all');

  useEffect(() => {
    fetchReports();
  }, []);

  useEffect(() => {
    filterReports();
  }, [reports, searchTerm, dateFilter, statusFilter, branchFilter]);

  const fetchReports = async () => {
    setLoading(true);
    try {
      // Enhanced demo data
      const demoReports = [
        {
          id: 1,
          report_date: '2024-01-15',
          notes: 'تقرير مبيعات ممتاز لهذا اليوم مع زيادة في الطلب',
          users: { name: 'أحمد محمد', branches: { name: 'الفرع الرئيسي' } },
          report_items: [
            { quantity: 2, unit_price: 2500 },
            { quantity: 1, unit_price: 800 }
          ],
          total: 5800,
          status: 'approved',
          created_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          report_date: '2024-01-14',
          notes: 'تقرير يومي عادي مع مبيعات متوسطة',
          users: { name: 'فاطمة أحمد', branches: { name: 'فرع الشمال' } },
          report_items: [
            { quantity: 3, unit_price: 150 },
            { quantity: 2, unit_price: 300 }
          ],
          total: 1050,
          status: 'pending',
          created_at: '2024-01-14T14:20:00Z'
        },
        {
          id: 3,
          report_date: '2024-01-13',
          notes: 'مبيعات جيدة مع زيادة في الطلب على المنتجات التقنية',
          users: { name: 'محمد علي', branches: { name: 'فرع الجنوب' } },
          report_items: [
            { quantity: 1, unit_price: 2500 },
            { quantity: 4, unit_price: 200 }
          ],
          total: 3300,
          status: 'approved',
          created_at: '2024-01-13T16:45:00Z'
        },
        {
          id: 4,
          report_date: '2024-01-12',
          notes: 'تقرير نهاية الأسبوع مع مبيعات منخفضة',
          users: { name: 'سارة خالد', branches: { name: 'الفرع الرئيسي' } },
          report_items: [
            { quantity: 2, unit_price: 450 },
            { quantity: 1, unit_price: 300 }
          ],
          total: 1200,
          status: 'rejected',
          created_at: '2024-01-12T11:15:00Z'
        },
        {
          id: 5,
          report_date: '2024-01-11',
          notes: 'تقرير ممتاز مع تحقيق أهداف المبيعات',
          users: { name: 'عبدالله سعد', branches: { name: 'فرع الشرق' } },
          report_items: [
            { quantity: 3, unit_price: 800 },
            { quantity: 2, unit_price: 600 }
          ],
          total: 3600,
          status: 'approved',
          created_at: '2024-01-11T09:30:00Z'
        }
      ];

      setReports(demoReports);
    } catch (error) {
      console.error('Error loading reports:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterReports = () => {
    let filtered = reports;

    if (searchTerm) {
      filtered = filtered.filter(report =>
        report.users.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.notes.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.users.branches.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (dateFilter !== 'all') {
      const today = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setDate(today.getDate());
          filtered = filtered.filter(report => {
            const reportDate = new Date(report.report_date);
            return reportDate.toDateString() === filterDate.toDateString();
          });
          break;
        case 'week':
          filterDate.setDate(today.getDate() - 7);
          filtered = filtered.filter(report => {
            const reportDate = new Date(report.report_date);
            return reportDate >= filterDate;
          });
          break;
        case 'month':
          filterDate.setMonth(today.getMonth() - 1);
          filtered = filtered.filter(report => {
            const reportDate = new Date(report.report_date);
            return reportDate >= filterDate;
          });
          break;
      }
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(report => report.status === statusFilter);
    }

    if (branchFilter !== 'all') {
      filtered = filtered.filter(report => report.users.branches.name === branchFilter);
    }

    setFilteredReports(filtered);
  };

  const getUniqueBranches = () => {
    return [...new Set(reports.map(r => r.users.branches.name))];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'موافق عليه';
      case 'pending': return 'قيد المراجعة';
      case 'rejected': return 'مرفوض';
      default: return 'غير محدد';
    }
  };

  const handleExport = () => {
    // Export logic here
    alert('سيتم تصدير التقارير قريباً!');
  };

  const calculateTotalSales = () => {
    return filteredReports.reduce((sum, report) => sum + report.total, 0);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-gray-600">{t('loading')}</div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('reports')}</h1>
            <p className="text-gray-600">إدارة ومراجعة التقارير اليومية</p>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Link
              to="/create-report"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
            >
              {t('create_report')}
            </Link>
            <button
              onClick={handleExport}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
            >
              {t('export')}
            </button>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="text-blue-600 text-sm font-medium">إجمالي التقارير</div>
            <div className="text-2xl font-bold text-blue-900">{filteredReports.length}</div>
          </div>

          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <div className="text-green-600 text-sm font-medium">إجمالي المبيعات</div>
            <div className="text-2xl font-bold text-green-900">{formatCurrency(calculateTotalSales())}</div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <div className="text-yellow-600 text-sm font-medium">قيد المراجعة</div>
            <div className="text-2xl font-bold text-yellow-900">
              {filteredReports.filter(r => r.status === 'pending').length}
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <div className="text-purple-600 text-sm font-medium">متوسط المبيعات</div>
            <div className="text-2xl font-bold text-purple-900">
              {filteredReports.length > 0 ? formatCurrency(calculateTotalSales() / filteredReports.length) : formatCurrency(0)}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div>
            <input
              type="text"
              placeholder={t('search') + '...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع التواريخ</option>
              <option value="today">اليوم</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
            </select>
          </div>

          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع الحالات</option>
              <option value="approved">موافق عليه</option>
              <option value="pending">قيد المراجعة</option>
              <option value="rejected">مرفوض</option>
            </select>
          </div>

          <div>
            <select
              value={branchFilter}
              onChange={(e) => setBranchFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع الفروع</option>
              {getUniqueBranches().map(branch => (
                <option key={branch} value={branch}>{branch}</option>
              ))}
            </select>
          </div>

          <div>
            <button
              onClick={() => {
                setSearchTerm('');
                setDateFilter('all');
                setStatusFilter('all');
                setBranchFilter('all');
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              {t('clear')} الفلاتر
            </button>
          </div>
        </div>
      </div>

      {/* Reports Table */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التقرير
                </th>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الموظف
                </th>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الفرع
                </th>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredReports.map((report) => (
                <tr key={report.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">تقرير #{report.id}</div>
                    <div className="text-sm text-gray-500 max-w-xs truncate">{report.notes}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {report.users.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {report.users.branches.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(report.report_date).toLocaleDateString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatCurrency(report.total)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                      {getStatusText(report.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2 space-x-reverse">
                      <Link
                        to={`/reports/${report.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        {t('view')}
                      </Link>
                      {(user?.role === 'Manager' || user?.role === 'Supervisor') && (
                        <button className="text-green-600 hover:text-green-900">
                          {t('edit')}
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredReports.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>{t('no_reports_found')}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportsPage;
