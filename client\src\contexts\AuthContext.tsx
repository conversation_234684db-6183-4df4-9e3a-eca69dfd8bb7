import React, { createContext, useState, useContext, useEffect } from 'react';

interface AuthContextType {
    session: any | null; // Demo session object
    user: any | null;    // Demo user object
    login: (email: string, password: string) => Promise<any>;
    logout: () => Promise<any>;
    isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [session, setSession] = useState<any | null>(null);
    const [user, setUser] = useState<any | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Demo: Auto-login with demo user (Manager role for testing all features)
        const demoUser = {
            id: 'demo-user-123',
            email: '<EMAIL>',
            name: 'أحمد المدير',
            role: 'Manager',
            branch: 'الفرع الرئيسي',
            phone: '+966501234567',
            avatar: null
        };

        const demoSession = {
            user: demoUser,
            access_token: 'demo-token'
        };

        setUser(demoUser);
        setSession(demoSession);
        setLoading(false);
    }, []);

    const login = async (email: string, password: string) => {
        // Demo login - different users based on email
        let demoUser;

        if (email.includes('manager') || email.includes('admin')) {
            demoUser = {
                id: 'manager-123',
                email: email,
                name: 'أحمد المدير',
                role: 'Manager',
                branch: 'الفرع الرئيسي',
                phone: '+966501234567',
                avatar: null
            };
        } else if (email.includes('supervisor')) {
            demoUser = {
                id: 'supervisor-123',
                email: email,
                name: 'فاطمة المشرفة',
                role: 'Supervisor',
                branch: 'فرع الشمال',
                phone: '+966507654321',
                avatar: null
            };
        } else {
            demoUser = {
                id: 'employee-123',
                email: email,
                name: 'محمد الموظف',
                role: 'Employee',
                branch: 'فرع الجنوب',
                phone: '+966509876543',
                avatar: null
            };
        }

        const demoSession = {
            user: demoUser,
            access_token: 'demo-token'
        };

        setUser(demoUser);
        setSession(demoSession);

        return { user: demoUser, session: demoSession };
    };

    const logout = async () => {
        setUser(null);
        setSession(null);
    };

    const isAuthenticated = !!session;

    if (loading) {
        return <div>جاري تحميل المصادقة...</div>;
    }

    return (
        <AuthContext.Provider value={{ session, user, login, logout, isAuthenticated }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};