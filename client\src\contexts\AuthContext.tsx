import React, { createContext, useState, useContext, useEffect } from 'react';
import * as api from '../services/api';

interface AuthContextType {
    session: any | null;
    user: any | null;
    login: (email: string, password: string) => Promise<any>;
    logout: () => Promise<any>;
    isAuthenticated: boolean;
    loading: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [session, setSession] = useState<any | null>(null);
    const [user, setUser] = useState<any | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Check if user is already logged in
        const checkAuth = async () => {
            const token = localStorage.getItem('auth_token');
            const userData = localStorage.getItem('user_data');

            if (token && userData) {
                try {
                    const currentUser = await api.getCurrentUser();
                    setUser(currentUser);
                    setSession({ user: currentUser, access_token: token });
                } catch (error) {
                    // Token is invalid, clear storage
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_data');
                }
            }
            setLoading(false);
        };

        checkAuth();
    }, []);

    const login = async (email: string, password: string) => {
        try {
            const response = await api.login(email, password);

            if (response.success) {
                const { user: userData, token } = response.data;
                const session = { user: userData, access_token: token };

                setUser(userData);
                setSession(session);

                return { user: userData, session };
            } else {
                throw new Error(response.message || 'فشل في تسجيل الدخول');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    };

    const logout = async () => {
        try {
            await api.logout();
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            setUser(null);
            setSession(null);
        }
    };

    const isAuthenticated = !!session;

    if (loading) {
        return <div>جاري تحميل المصادقة...</div>;
    }

    return (
        <AuthContext.Provider value={{ session, user, login, logout, isAuthenticated, loading }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};