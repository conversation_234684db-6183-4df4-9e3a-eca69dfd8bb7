const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testAPI() {
  try {
    console.log('🧪 بدء اختبار API endpoints...\n');

    // 1. اختبار تسجيل الدخول
    console.log('1️⃣ اختبار تسجيل الدخول...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });

    if (loginResponse.data.success) {
      console.log('✅ تسجيل الدخول نجح');
      console.log(`👤 المستخدم: ${loginResponse.data.data.user.name}`);
      console.log(`🏢 الدور: ${loginResponse.data.data.user.role}`);
      
      const token = loginResponse.data.data.token;
      const headers = { Authorization: `Bearer ${token}` };

      // 2. اختبار الحصول على الفروع
      console.log('\n2️⃣ اختبار الحصول على الفروع...');
      const branchesResponse = await axios.get(`${BASE_URL}/branches`, { headers });
      console.log(`✅ تم الحصول على ${branchesResponse.data.data.branches.length} فرع`);

      // 3. اختبار الحصول على المنتجات
      console.log('\n3️⃣ اختبار الحصول على المنتجات...');
      const productsResponse = await axios.get(`${BASE_URL}/products`, { headers });
      console.log(`✅ تم الحصول على ${productsResponse.data.data.products.length} منتج`);

      // 4. اختبار الحصول على التقارير
      console.log('\n4️⃣ اختبار الحصول على التقارير...');
      const reportsResponse = await axios.get(`${BASE_URL}/reports`, { headers });
      console.log(`✅ تم الحصول على ${reportsResponse.data.data.reports.length} تقرير`);

      // 5. اختبار الحصول على المستخدمين
      console.log('\n5️⃣ اختبار الحصول على المستخدمين...');
      const usersResponse = await axios.get(`${BASE_URL}/users`, { headers });
      console.log(`✅ تم الحصول على ${usersResponse.data.data.users.length} مستخدم`);

      // 6. اختبار إحصائيات الوحة التحكم
      console.log('\n6️⃣ اختبار إحصائيات الوحة التحكم...');
      const statsResponse = await axios.get(`${BASE_URL}/stats/dashboard`, { headers });
      console.log('✅ تم الحصول على إحصائيات الوحة التحكم');
      console.log(`📊 إجمالي التقارير: ${statsResponse.data.data.summary.total_reports}`);
      console.log(`💰 إجمالي المبيعات: ${statsResponse.data.data.summary.total_sales} BHD`);

      // 7. اختبار إنشاء تقرير جديد
      console.log('\n7️⃣ اختبار إنشاء تقرير جديد...');
      const newReportData = {
        report_date: new Date().toISOString().split('T')[0],
        notes: 'تقرير اختبار من API',
        items: [
          {
            product_id: 1,
            quantity: 2,
            unit_price: 450.00
          },
          {
            product_id: 2,
            quantity: 1,
            unit_price: 380.00
          }
        ]
      };

      const createReportResponse = await axios.post(`${BASE_URL}/reports`, newReportData, { headers });
      if (createReportResponse.data.success) {
        console.log('✅ تم إنشاء التقرير بنجاح');
        console.log(`📝 رقم التقرير: ${createReportResponse.data.data.report.id}`);
      }

      console.log('\n🎉 جميع الاختبارات نجحت! API يعمل بشكل صحيح');

    } else {
      console.log('❌ فشل تسجيل الدخول');
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.response?.data || error.message);
  }
}

// تشغيل الاختبارات
testAPI();
