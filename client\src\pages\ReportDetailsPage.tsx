import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';

const ReportDetailsPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { formatCurrency } = useCurrency();

  const [report, setReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    loadReportDetails();
  }, [id]);

  const loadReportDetails = async () => {
    try {
      setIsLoading(true);
      // Demo report data
      const demoReport = {
        id: parseInt(id || '1'),
        report_date: '2024-01-15',
        notes: 'تقرير مبيعات ممتاز لهذا اليوم مع زيادة في الطلب على المنتجات التقنية',
        user: {
          name: 'أحمد محمد',
          email: '<EMAIL>',
          branch: 'الفرع الرئيسي'
        },
        items: [
          {
            id: 1,
            product: { model_name: 'لابتوب ديل XPS 13' },
            quantity: 2,
            unit_price: 2500,
            total: 5000
          },
          {
            id: 2,
            product: { model_name: 'ماوس لوجيتك MX Master' },
            quantity: 5,
            unit_price: 150,
            total: 750
          },
          {
            id: 3,
            product: { model_name: 'شاشة سامسونج 24 بوصة' },
            quantity: 1,
            unit_price: 800,
            total: 800
          }
        ],
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      };

      setReport(demoReport);
    } catch (error) {
      console.error('Error loading report details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotal = () => {
    if (!report?.items) return 0;
    return report.items.reduce((sum: number, item: any) => sum + item.total, 0);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // Save logic here
    setIsEditing(false);
    alert('تم حفظ التغييرات بنجاح!');
  };

  const handleCancel = () => {
    setIsEditing(false);
    loadReportDetails(); // Reload original data
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg text-gray-600">{t('loading')}</div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg text-red-600">التقرير غير موجود</div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8 print:mb-4">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">تفاصيل التقرير #{report.id}</h1>
            <p className="text-gray-600">عرض تفصيلي للتقرير اليومي</p>
          </div>

          <div className="flex space-x-2 space-x-reverse print:hidden">
            <button
              onClick={() => navigate('/reports')}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors"
            >
              العودة للتقارير
            </button>
            <button
              onClick={handlePrint}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md font-medium transition-colors"
            >
              {t('print')}
            </button>
            {(user?.role === 'Manager' || user?.role === 'Supervisor') && (
              <button
                onClick={isEditing ? handleSave : handleEdit}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors"
              >
                {isEditing ? t('save') : t('edit')}
              </button>
            )}
            {isEditing && (
              <button
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium transition-colors"
              >
                {t('cancel')}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Report Info */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">معلومات التقرير</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ التقرير</label>
            <p className="text-gray-900">{new Date(report.report_date).toLocaleDateString('ar-SA')}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">اسم الموظف</label>
            <p className="text-gray-900">{report.user.name}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
            <p className="text-gray-900">{report.user.branch}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
            <p className="text-gray-900">{report.user.email}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
            <p className="text-gray-900">{new Date(report.created_at).toLocaleString('ar-SA')}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">آخر تحديث</label>
            <p className="text-gray-900">{new Date(report.updated_at).toLocaleString('ar-SA')}</p>
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">الملاحظات</label>
          {isEditing ? (
            <textarea
              value={report.notes}
              onChange={(e) => setReport({ ...report, notes: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          ) : (
            <p className="text-gray-900 bg-gray-50 p-3 rounded-md">
              {report.notes || 'لا توجد ملاحظات'}
            </p>
          )}
        </div>
      </div>

      {/* Report Items */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">عناصر التقرير</h2>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-right py-3 text-sm font-medium text-gray-600">#</th>
                <th className="text-right py-3 text-sm font-medium text-gray-600">المنتج</th>
                <th className="text-right py-3 text-sm font-medium text-gray-600">الكمية</th>
                <th className="text-right py-3 text-sm font-medium text-gray-600">سعر الوحدة</th>
                <th className="text-right py-3 text-sm font-medium text-gray-600">الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              {report.items.map((item: any, index: number) => (
                <tr key={item.id} className="border-b border-gray-100">
                  <td className="py-3 text-sm text-gray-900">{index + 1}</td>
                  <td className="py-3 text-sm text-gray-900">{item.product.model_name}</td>
                  <td className="py-3 text-sm text-gray-900">{item.quantity}</td>
                  <td className="py-3 text-sm text-gray-900">{formatCurrency(item.unit_price)}</td>
                  <td className="py-3 text-sm font-medium text-gray-900">{formatCurrency(item.total)}</td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="border-t-2 border-gray-300">
                <td colSpan={4} className="py-3 text-sm font-bold text-gray-900 text-right">الإجمالي الكلي:</td>
                <td className="py-3 text-lg font-bold text-blue-600">{formatCurrency(calculateTotal())}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 print:hidden">
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="text-blue-600 text-sm font-medium">عدد العناصر</div>
          <div className="text-2xl font-bold text-blue-900">{report.items.length}</div>
        </div>

        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
          <div className="text-green-600 text-sm font-medium">إجمالي الكمية</div>
          <div className="text-2xl font-bold text-green-900">
            {report.items.reduce((sum: number, item: any) => sum + item.quantity, 0)}
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
          <div className="text-purple-600 text-sm font-medium">متوسط سعر العنصر</div>
          <div className="text-2xl font-bold text-purple-900">
            {formatCurrency(calculateTotal() / report.items.length)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportDetailsPage;
