const express = require('express');
const { pool } = require('../config/database');
const { authenticateToken } = require('./auth');
const router = express.Router();

// الحصول على جميع التقارير
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { status, date_from, date_to, branch_id } = req.query;
    let query = `
      SELECT r.*, u.name as user_name, b.name as branch_name,
             COUNT(ri.id) as items_count,
             SUM(ri.quantity * ri.unit_price) as total_amount
      FROM reports r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN branches b ON u.branch_id = b.id
      LEFT JOIN report_items ri ON r.id = ri.report_id
      WHERE 1=1
    `;
    let params = [];

    // تطبيق فلاتر حسب دور المستخدم
    if (req.user.role === 'employee') {
      query += ' AND r.user_id = ?';
      params.push(req.user.userId);
    } else if (req.user.role === 'supervisor') {
      // المشرف يرى تقارير فرعه فقط
      const [userBranch] = await pool.execute(
        'SELECT branch_id FROM users WHERE id = ?',
        [req.user.userId]
      );
      if (userBranch.length > 0) {
        query += ' AND r.branch_id = ?';
        params.push(userBranch[0].branch_id);
      }
    }
    // المدير يرى جميع التقارير

    if (status) {
      query += ' AND r.status = ?';
      params.push(status);
    }

    if (date_from) {
      query += ' AND DATE(r.report_date) >= ?';
      params.push(date_from);
    }

    if (date_to) {
      query += ' AND DATE(r.report_date) <= ?';
      params.push(date_to);
    }

    if (branch_id) {
      query += ' AND r.branch_id = ?';
      params.push(branch_id);
    }

    query += ' GROUP BY r.id ORDER BY r.report_date DESC, r.created_at DESC';

    const [reports] = await pool.execute(query, params);

    res.json({
      success: true,
      data: { reports }
    });

  } catch (error) {
    console.error('خطأ في الحصول على التقارير:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على تقرير محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على التقرير
    const [reports] = await pool.execute(`
      SELECT r.*, u.name as user_name, b.name as branch_name
      FROM reports r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN branches b ON r.branch_id = b.id
      WHERE r.id = ?
    `, [id]);

    if (reports.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'التقرير غير موجود'
      });
    }

    const report = reports[0];

    // التحقق من صلاحيات الوصول
    if (req.user.role === 'employee' && report.user_id !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لعرض هذا التقرير'
      });
    }

    if (req.user.role === 'supervisor') {
      const [userBranch] = await pool.execute(
        'SELECT branch_id FROM users WHERE id = ?',
        [req.user.userId]
      );
      if (userBranch.length > 0 && report.branch_id !== userBranch[0].branch_id) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية لعرض هذا التقرير'
        });
      }
    }

    // الحصول على عناصر التقرير
    const [items] = await pool.execute(`
      SELECT ri.*, p.name as product_name, p.category as product_category
      FROM report_items ri
      LEFT JOIN products p ON ri.product_id = p.id
      WHERE ri.report_id = ?
      ORDER BY ri.id
    `, [id]);

    report.items = items;

    res.json({
      success: true,
      data: { report }
    });

  } catch (error) {
    console.error('خطأ في الحصول على التقرير:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء تقرير جديد
router.post('/', authenticateToken, async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    const { report_date, notes, items } = req.body;

    if (!report_date || !items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'تاريخ التقرير والعناصر مطلوبة'
      });
    }

    // الحصول على معلومات المستخدم
    const [users] = await connection.execute(
      'SELECT branch_id FROM users WHERE id = ?',
      [req.user.userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const userBranchId = users[0].branch_id;

    // إنشاء التقرير
    const [reportResult] = await connection.execute(`
      INSERT INTO reports (user_id, branch_id, report_date, notes, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, 'pending', NOW(), NOW())
    `, [req.user.userId, userBranchId, report_date, notes || null]);

    const reportId = reportResult.insertId;

    // إضافة عناصر التقرير
    for (const item of items) {
      if (!item.product_id || !item.quantity || !item.unit_price) {
        throw new Error('معلومات العنصر غير مكتملة');
      }

      await connection.execute(`
        INSERT INTO report_items (report_id, product_id, quantity, unit_price, total_price, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, [reportId, item.product_id, item.quantity, item.unit_price, item.quantity * item.unit_price]);
    }

    await connection.commit();

    // الحصول على التقرير المنشأ مع تفاصيله
    const [newReport] = await pool.execute(`
      SELECT r.*, u.name as user_name, b.name as branch_name
      FROM reports r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN branches b ON r.branch_id = b.id
      WHERE r.id = ?
    `, [reportId]);

    res.status(201).json({
      success: true,
      message: 'تم إنشاء التقرير بنجاح',
      data: { report: newReport[0] }
    });

  } catch (error) {
    await connection.rollback();
    console.error('خطأ في إنشاء التقرير:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'حدث خطأ في الخادم'
    });
  } finally {
    connection.release();
  }
});

// تحديث حالة التقرير (للمشرفين والمدراء)
router.patch('/:id/status', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (!['supervisor', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لتحديث حالة التقرير'
      });
    }

    const { id } = req.params;
    const { status, review_notes } = req.body;

    const validStatuses = ['pending', 'approved', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'حالة التقرير غير صالحة'
      });
    }

    // التحقق من وجود التقرير
    const [reports] = await pool.execute(
      'SELECT * FROM reports WHERE id = ?',
      [id]
    );

    if (reports.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'التقرير غير موجود'
      });
    }

    const report = reports[0];

    // التحقق من صلاحيات الوصول للمشرف
    if (req.user.role === 'supervisor') {
      const [userBranch] = await pool.execute(
        'SELECT branch_id FROM users WHERE id = ?',
        [req.user.userId]
      );
      if (userBranch.length > 0 && report.branch_id !== userBranch[0].branch_id) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية لتحديث هذا التقرير'
        });
      }
    }

    // تحديث حالة التقرير
    await pool.execute(`
      UPDATE reports 
      SET status = ?, review_notes = ?, reviewed_by = ?, reviewed_at = NOW(), updated_at = NOW()
      WHERE id = ?
    `, [status, review_notes || null, req.user.userId, id]);

    res.json({
      success: true,
      message: 'تم تحديث حالة التقرير بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحديث حالة التقرير:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// حذف تقرير (للمؤلف فقط أو المدراء)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من وجود التقرير
    const [reports] = await pool.execute(
      'SELECT * FROM reports WHERE id = ?',
      [id]
    );

    if (reports.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'التقرير غير موجود'
      });
    }

    const report = reports[0];

    // التحقق من صلاحيات الحذف
    if (req.user.role !== 'manager' && report.user_id !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لحذف هذا التقرير'
      });
    }

    // التحقق من حالة التقرير (لا يمكن حذف التقارير المعتمدة)
    if (report.status === 'approved') {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن حذف التقرير المعتمد'
      });
    }

    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // حذف عناصر التقرير
      await connection.execute(
        'DELETE FROM report_items WHERE report_id = ?',
        [id]
      );

      // حذف التقرير
      await connection.execute(
        'DELETE FROM reports WHERE id = ?',
        [id]
      );

      await connection.commit();

      res.json({
        success: true,
        message: 'تم حذف التقرير بنجاح'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('خطأ في حذف التقرير:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
