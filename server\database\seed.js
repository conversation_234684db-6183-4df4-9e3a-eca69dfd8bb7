const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');

async function seedDatabase() {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('🌱 بدء إدراج البيانات التجريبية...');

    // إدراج الفروع
    console.log('📍 إدراج الفروع...');
    const branches = [
      ['الفرع الرئيسي', 'المنامة، مملكة البحرين', '+973-1234-5678', '<EMAIL>'],
      ['فرع المحرق', 'المحرق، مملكة البحرين', '+973-1234-5679', '<EMAIL>'],
      ['فرع الرفاع', 'الرفاع، مملكة البحرين', '+973-1234-5680', '<EMAIL>'],
      ['فرع عيسى تاون', 'عيسى تاون، مملكة البحرين', '+973-1234-5681', '<EMAIL>']
    ];

    for (const branch of branches) {
      await connection.execute(
        'INSERT INTO branches (name, location, phone, email, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())',
        branch
      );
    }

    // إدراج المنتجات
    console.log('📦 إدراج المنتجات...');
    const products = [
      // منتجات إلكترونية
      ['لابتوب Dell XPS 13', 'Dell', 'إلكترونيات', 'لابتوب عالي الأداء للأعمال', 450.000],
      ['هاتف iPhone 15', 'Apple', 'إلكترونيات', 'أحدث هاتف ذكي من آبل', 380.000],
      ['سماعات AirPods Pro', 'Apple', 'إلكترونيات', 'سماعات لاسلكية بتقنية إلغاء الضوضاء', 95.000],
      ['شاشة Samsung 27"', 'Samsung', 'إلكترونيات', 'شاشة عرض عالية الدقة', 120.000],
      ['كيبورد ميكانيكي', 'Corsair', 'إلكترونيات', 'لوحة مفاتيح للألعاب', 45.000],

      // ملابس
      ['قميص رجالي قطني', 'Generic', 'ملابس', 'قميص كلاسيكي للرجال', 25.000],
      ['فستان نسائي صيفي', 'Generic', 'ملابس', 'فستان أنيق للصيف', 35.000],
      ['بنطلون جينز', 'Levi\'s', 'ملابس', 'بنطلون جينز كلاسيكي', 30.000],
      ['حذاء رياضي', 'Nike', 'ملابس', 'حذاء مريح للرياضة', 40.000],
      ['جاكيت شتوي', 'Adidas', 'ملابس', 'جاكيت دافئ للشتاء', 55.000],

      // مستحضرات تجميل
      ['كريم مرطب للوجه', 'L\'Oreal', 'تجميل', 'كريم طبيعي للعناية بالبشرة', 18.000],
      ['شامبو للشعر الجاف', 'Pantene', 'تجميل', 'شامبو مغذي للشعر', 12.000],
      ['أحمر شفاه', 'MAC', 'تجميل', 'أحمر شفاه طويل الثبات', 15.000],
      ['عطر رجالي', 'Hugo Boss', 'تجميل', 'عطر فاخر للرجال', 65.000],
      ['مكياج أساس', 'Maybelline', 'تجميل', 'كريم أساس طبيعي', 22.000],

      // أدوات منزلية
      ['مكنسة كهربائية', 'Dyson', 'منزلية', 'مكنسة قوية للتنظيف', 85.000],
      ['خلاط كهربائي', 'Philips', 'منزلية', 'خلاط متعدد الاستخدامات', 35.000],
      ['طقم أواني طبخ', 'Tefal', 'منزلية', 'طقم كامل من الستانلس ستيل', 75.000],
      ['مكواة بخار', 'Braun', 'منزلية', 'مكواة حديثة بالبخار', 28.000],
      ['غسالة ملابس', 'LG', 'منزلية', 'غسالة أوتوماتيكية 7 كيلو', 180.000]
    ];

    for (const product of products) {
      await connection.execute(
        'INSERT INTO products (model_name, brand, category, description, unit_price, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
        product
      );
    }

    // إدراج المستخدمين
    console.log('👥 إدراج المستخدمين...');
    const hashedPassword = await bcrypt.hash('123456', 10);

    const users = [
      // مدير عام
      ['أحمد محمد الخليفي', '<EMAIL>', hashedPassword, 'Manager', 1, '+973-3333-1111'],

      // مشرفين
      ['فاطمة علي البحراني', '<EMAIL>', hashedPassword, 'Supervisor', 1, '+973-3333-2222'],
      ['محمد حسن العلوي', '<EMAIL>', hashedPassword, 'Supervisor', 2, '+973-3333-3333'],
      ['سارة أحمد الكعبي', '<EMAIL>', hashedPassword, 'Supervisor', 3, '+973-3333-4444'],

      // موظفين
      ['علي محمد السالم', '<EMAIL>', hashedPassword, 'Employee', 1, '+973-3333-5555'],
      ['مريم أحمد الزهراني', '<EMAIL>', hashedPassword, 'Employee', 1, '+973-3333-6666'],
      ['خالد عبدالله المطوع', '<EMAIL>', hashedPassword, 'Employee', 2, '+973-3333-7777'],
      ['نورا سعد الدوسري', '<EMAIL>', hashedPassword, 'Employee', 2, '+973-3333-8888'],
      ['يوسف محمد الشمري', '<EMAIL>', hashedPassword, 'Employee', 3, '+973-3333-9999'],
      ['هدى علي الخالدي', '<EMAIL>', hashedPassword, 'Employee', 3, '+973-3333-0000']
    ];

    for (const user of users) {
      await connection.execute(
        'INSERT INTO users (name, email, password, role, branch_id, phone, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
        user
      );
    }

    // إدراج تقارير تجريبية
    console.log('📊 إدراج التقارير التجريبية...');

    // تقرير معتمد من الأمس
    const [report1] = await connection.execute(`
      INSERT INTO reports (user_id, report_date, notes, status, created_at, updated_at)
      VALUES (3, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'مبيعات ممتازة اليوم', 'approved', NOW(), NOW())
    `);

    // إضافة عناصر للتقرير الأول
    const report1Items = [
      [report1.insertId, 1, 2, 450.000], // لابتوب Dell XPS 13
      [report1.insertId, 3, 5, 95.000],  // سماعات AirPods Pro
      [report1.insertId, 6, 3, 25.000],  // قميص رجالي قطني
      [report1.insertId, 11, 4, 18.000]  // كريم مرطب للوجه
    ];

    for (const item of report1Items) {
      await connection.execute(
        'INSERT INTO report_items (report_id, product_id, quantity, unit_price, created_at) VALUES (?, ?, ?, ?, NOW())',
        item
      );
    }

    // تقرير معلق من اليوم
    const [report2] = await connection.execute(`
      INSERT INTO reports (user_id, report_date, notes, status, created_at, updated_at)
      VALUES (4, CURDATE(), 'تقرير مبيعات اليوم', 'submitted', NOW(), NOW())
    `);

    // إضافة عناصر للتقرير الثاني
    const report2Items = [
      [report2.insertId, 2, 1, 380.000], // هاتف iPhone 15
      [report2.insertId, 4, 2, 120.000], // شاشة Samsung 27"
      [report2.insertId, 9, 1, 40.000],  // حذاء رياضي
      [report2.insertId, 16, 1, 85.000]  // مكنسة كهربائية
    ];

    for (const item of report2Items) {
      await connection.execute(
        'INSERT INTO report_items (report_id, product_id, quantity, unit_price, created_at) VALUES (?, ?, ?, ?, NOW())',
        item
      );
    }

    // تقرير من الأسبوع الماضي
    const [report3] = await connection.execute(`
      INSERT INTO reports (user_id, report_date, notes, status, created_at, updated_at)
      VALUES (5, DATE_SUB(CURDATE(), INTERVAL 5 DAY), 'مبيعات الأسبوع الماضي', 'approved', NOW(), NOW())
    `);

    // إضافة عناصر للتقرير الثالث
    const report3Items = [
      [report3.insertId, 14, 3, 65.000], // عطر رجالي
      [report3.insertId, 18, 1, 75.000], // طقم أواني طبخ
      [report3.insertId, 7, 2, 35.000],  // فستان نسائي صيفي
      [report3.insertId, 12, 6, 12.000]  // شامبو للشعر الجاف
    ];

    for (const item of report3Items) {
      await connection.execute(
        'INSERT INTO report_items (report_id, product_id, quantity, unit_price, created_at) VALUES (?, ?, ?, ?, NOW())',
        item
      );
    }

    await connection.commit();
    console.log('✅ تم إدراج البيانات التجريبية بنجاح!');

    // طباعة ملخص البيانات المدرجة
    console.log('\n📋 ملخص البيانات المدرجة:');
    console.log(`- ${branches.length} فرع`);
    console.log(`- ${products.length} منتج`);
    console.log(`- ${users.length} مستخدم`);
    console.log('- 3 تقارير مع عناصرها');

    console.log('\n🔐 بيانات تسجيل الدخول:');
    console.log('المدير: <EMAIL> / 123456');
    console.log('المشرف: <EMAIL> / 123456');
    console.log('الموظف: <EMAIL> / 123456');
    console.log('\n📝 ملاحظة: يمكنك استخدام أي من الحسابات المذكورة أعلاه لتسجيل الدخول');

  } catch (error) {
    await connection.rollback();
    console.error('❌ خطأ في إدراج البيانات التجريبية:', error);
    throw error;
  } finally {
    connection.release();
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('🎉 انتهى إدراج البيانات التجريبية بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في إدراج البيانات التجريبية:', error);
      process.exit(1);
    });
}

module.exports = { seedDatabase };
