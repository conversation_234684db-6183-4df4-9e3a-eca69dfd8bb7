import React, { createContext, useContext, useState, useEffect } from 'react';

export interface Currency {
  code: string;
  symbol: string;
  name_ar: string;
  name_en: string;
  decimal_places: number;
}

export const SUPPORTED_CURRENCIES: Currency[] = [
  {
    code: 'BHD',
    symbol: 'د.ب',
    name_ar: 'الدينار البحريني',
    name_en: 'Bahraini Dinar',
    decimal_places: 3
  },
  {
    code: 'SAR',
    symbol: 'ر.س',
    name_ar: 'الريال السعودي',
    name_en: 'Saudi Riyal',
    decimal_places: 2
  },
  {
    code: 'USD',
    symbol: '$',
    name_ar: 'الدولار الأمريكي',
    name_en: 'US Dollar',
    decimal_places: 2
  },
  {
    code: 'EUR',
    symbol: '€',
    name_ar: 'اليورو',
    name_en: 'Euro',
    decimal_places: 2
  }
];

interface CurrencyContextType {
  currentCurrency: Currency;
  setCurrency: (currency: Currency) => void;
  formatCurrency: (amount: number, showSymbol?: boolean) => string;
  getSupportedCurrencies: () => Currency[];
  getCurrencyByCode: (code: string) => Currency | undefined;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};

interface CurrencyProviderProps {
  children: React.ReactNode;
}

export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({ children }) => {
  const [currentCurrency, setCurrentCurrency] = useState<Currency>(SUPPORTED_CURRENCIES[0]); // Default to BHD

  useEffect(() => {
    // Load saved currency from localStorage
    const savedCurrencyCode = localStorage.getItem('selectedCurrency');
    if (savedCurrencyCode) {
      const savedCurrency = SUPPORTED_CURRENCIES.find(c => c.code === savedCurrencyCode);
      if (savedCurrency) {
        setCurrentCurrency(savedCurrency);
      }
    }
  }, []);

  const setCurrency = (currency: Currency) => {
    setCurrentCurrency(currency);
    localStorage.setItem('selectedCurrency', currency.code);
  };

  const formatCurrency = (amount: number, showSymbol: boolean = true): string => {
    if (isNaN(amount)) return '0';
    
    const formattedAmount = amount.toFixed(currentCurrency.decimal_places);
    const parts = formattedAmount.split('.');
    
    // Add thousand separators
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    const finalAmount = parts.join('.');
    
    if (showSymbol) {
      // For Arabic currencies (BHD, SAR), put symbol after the number
      if (currentCurrency.code === 'BHD' || currentCurrency.code === 'SAR') {
        return `${finalAmount} ${currentCurrency.symbol}`;
      } else {
        // For Western currencies (USD, EUR), put symbol before the number
        return `${currentCurrency.symbol}${finalAmount}`;
      }
    }
    
    return finalAmount;
  };

  const getSupportedCurrencies = (): Currency[] => {
    return SUPPORTED_CURRENCIES;
  };

  const getCurrencyByCode = (code: string): Currency | undefined => {
    return SUPPORTED_CURRENCIES.find(c => c.code === code);
  };

  const value: CurrencyContextType = {
    currentCurrency,
    setCurrency,
    formatCurrency,
    getSupportedCurrencies,
    getCurrencyByCode
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};

export default CurrencyContext;
