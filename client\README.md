# Daily Reports App - Client

This is the client-side application for the Daily Reports App, built with React, TypeScript, and Vite.

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

- Node.js (v18.x or later recommended)
- npm or yarn

### Installation

1. Clone the repository (if you haven't already):
   ```bash
   git clone <your-repository-url>
   ```
2. Navigate to the client directory:
   ```bash
   cd daily-reports-app/client
   ```
3. Install the dependencies:
   ```bash
   npm install
   ```
   or
   ```bash
   yarn install
   ```

## Available Scripts

In the project directory, you can run:

### `npm run dev`

Runs the app in the development mode.
Open [http://localhost:5173](http://localhost:5173) (or another port if 5173 is busy) to view it in the browser.

The page will reload if you make edits.
You will also see any lint errors in the console.

### `npm run build`

Builds the app for production to the `dist` folder.
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.
Your app is ready to be deployed!

### `npm run lint`

Lints the project files for code quality and style issues.

### `npm run preview`

Runs a local static web server that serves the files from `dist`. It's a good way to check if the production build works correctly on your local machine before deploying.

## Deployment

To deploy this application, you need to:

1.  **Build the application:**
    ```bash
    npm run build
    ```
2.  **Deploy the `dist` directory:**
    The `npm run build` command will create a `dist` directory in the project root. This directory contains all the static files (HTML, CSS, JavaScript) needed to run the application.

    You can deploy the contents of this `dist` folder to any static hosting service, such as:
    - Vercel
    - Netlify
    - GitHub Pages
    - Your own server with Nginx or Apache.

    Most of these services can be configured to automatically pull your code from a Git repository, run the build command, and deploy the result.
