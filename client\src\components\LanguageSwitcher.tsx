import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="flex space-x-2">
      <button
        onClick={() => changeLanguage('en')}
        className={`px-3 py-1 rounded-md ${i18n.language === 'en' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}`}
      >
        English
      </button>
      <button
        onClick={() => changeLanguage('ar')}
        className={`px-3 py-1 rounded-md ${i18n.language === 'ar' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}`}
        dir="rtl"
      >
        العربية
      </button>
    </div>
  );
};

export default LanguageSwitcher;
