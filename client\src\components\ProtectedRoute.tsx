import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
    allowedRoles?: ('Employee' | 'Supervisor' | 'Manager')[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ allowedRoles }) => {
    const { isAuthenticated, user } = useAuth();

    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }

    if (allowedRoles && user && !allowedRoles.includes(user.role)) {
        // Optionally, redirect to an unauthorized page or show a message
        return <div className="text-center text-red-500 mt-10">You do not have permission to view this page.</div>;
    }

    return <Outlet />;
};

export default ProtectedRoute;
