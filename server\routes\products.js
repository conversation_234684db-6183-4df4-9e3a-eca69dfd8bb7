const express = require('express');
const { pool } = require('../config/database');
const { authenticateToken } = require('./auth');
const router = express.Router();

// الحصول على جميع المنتجات
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { category, search } = req.query;
    let query = 'SELECT * FROM products WHERE 1=1';
    let params = [];

    if (category) {
      query += ' AND category = ?';
      params.push(category);
    }

    if (search) {
      query += ' AND (model_name LIKE ? OR description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    query += ' ORDER BY model_name';

    const [products] = await pool.execute(query, params);

    res.json({
      success: true,
      data: { products }
    });

  } catch (error) {
    console.error('خطأ في الحصول على المنتجات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على منتج محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const [products] = await pool.execute(
      'SELECT * FROM products WHERE id = ?',
      [id]
    );

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    res.json({
      success: true,
      data: { product: products[0] }
    });

  } catch (error) {
    console.error('خطأ في الحصول على المنتج:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء منتج جديد (للمشرفين والمدراء)
router.post('/', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (!['supervisor', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لإنشاء منتج جديد'
      });
    }

    const { model_name, brand, description, category, unit_price } = req.body;

    if (!model_name || !category || !unit_price) {
      return res.status(400).json({
        success: false,
        message: 'اسم المنتج والفئة والسعر مطلوبة'
      });
    }

    // التحقق من عدم وجود منتج بنفس الاسم
    const [existingProducts] = await pool.execute(
      'SELECT id FROM products WHERE model_name = ?',
      [model_name]
    );

    if (existingProducts.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد منتج بهذا الاسم بالفعل'
      });
    }

    // إنشاء المنتج الجديد
    const [result] = await pool.execute(
      `INSERT INTO products (model_name, brand, description, category, unit_price, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
      [model_name, brand || null, description || null, category, unit_price]
    );

    // الحصول على المنتج المنشأ
    const [newProduct] = await pool.execute(
      'SELECT * FROM products WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المنتج بنجاح',
      data: { product: newProduct[0] }
    });

  } catch (error) {
    console.error('خطأ في إنشاء المنتج:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تحديث منتج (للمشرفين والمدراء)
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (!['supervisor', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لتحديث المنتج'
      });
    }

    const { id } = req.params;
    const { name, description, category, unit_price, currency } = req.body;

    if (!name || !category || !unit_price || !currency) {
      return res.status(400).json({
        success: false,
        message: 'اسم المنتج والفئة والسعر والعملة مطلوبة'
      });
    }

    // التحقق من صحة العملة
    const validCurrencies = ['BHD', 'SAR', 'USD', 'EUR'];
    if (!validCurrencies.includes(currency)) {
      return res.status(400).json({
        success: false,
        message: 'العملة غير صالحة'
      });
    }

    // التحقق من وجود المنتج
    const [existingProduct] = await pool.execute(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (existingProduct.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    // التحقق من عدم وجود منتج آخر بنفس الاسم
    const [duplicateProducts] = await pool.execute(
      'SELECT id FROM products WHERE name = ? AND id != ?',
      [name, id]
    );

    if (duplicateProducts.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد منتج آخر بهذا الاسم'
      });
    }

    // تحديث المنتج
    await pool.execute(
      `UPDATE products 
       SET name = ?, description = ?, category = ?, unit_price = ?, currency = ?, updated_at = NOW() 
       WHERE id = ?`,
      [name, description || null, category, unit_price, currency, id]
    );

    // الحصول على المنتج المحدث
    const [updatedProduct] = await pool.execute(
      'SELECT * FROM products WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: 'تم تحديث المنتج بنجاح',
      data: { product: updatedProduct[0] }
    });

  } catch (error) {
    console.error('خطأ في تحديث المنتج:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// حذف منتج (للمشرفين والمدراء)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (!['supervisor', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لحذف المنتج'
      });
    }

    const { id } = req.params;

    // التحقق من وجود المنتج
    const [existingProduct] = await pool.execute(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (existingProduct.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المنتج غير موجود'
      });
    }

    // التحقق من عدم وجود عناصر تقارير مرتبطة بالمنتج
    const [reportItems] = await pool.execute(
      'SELECT id FROM report_items WHERE product_id = ?',
      [id]
    );

    if (reportItems.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن حذف المنتج لوجود تقارير مرتبطة به'
      });
    }

    // حذف المنتج (soft delete)
    await pool.execute(
      'UPDATE products SET is_active = false, updated_at = NOW() WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: 'تم حذف المنتج بنجاح'
    });

  } catch (error) {
    console.error('خطأ في حذف المنتج:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على فئات المنتجات
router.get('/categories/list', authenticateToken, async (req, res) => {
  try {
    const [categories] = await pool.execute(
      'SELECT DISTINCT category FROM products WHERE is_active = true ORDER BY category'
    );

    res.json({
      success: true,
      data: { categories: categories.map(c => c.category) }
    });

  } catch (error) {
    console.error('خطأ في الحصول على فئات المنتجات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
