const { pool } = require('../config/database');

async function clearDatabase() {
  const connection = await pool.getConnection();
  
  try {
    console.log('🧹 بدء مسح البيانات من قاعدة البيانات...');

    // تعطيل فحص المفاتيح الخارجية مؤقتاً
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');

    // مسح البيانات من الجداول بالترتيب الصحيح
    await connection.execute('DELETE FROM report_items');
    console.log('✅ تم مسح عناصر التقارير');

    await connection.execute('DELETE FROM reports');
    console.log('✅ تم مسح التقارير');

    await connection.execute('DELETE FROM users');
    console.log('✅ تم مسح المستخدمين');

    await connection.execute('DELETE FROM products');
    console.log('✅ تم مسح المنتجات');

    await connection.execute('DELETE FROM branches');
    console.log('✅ تم مسح الفروع');

    // إعادة تعيين AUTO_INCREMENT
    await connection.execute('ALTER TABLE report_items AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE reports AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE users AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE products AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE branches AUTO_INCREMENT = 1');

    // إعادة تفعيل فحص المفاتيح الخارجية
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');

    console.log('🎉 تم مسح جميع البيانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في مسح البيانات:', error);
    throw error;
  } finally {
    connection.release();
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  clearDatabase()
    .then(() => {
      console.log('✨ انتهى مسح البيانات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في مسح البيانات:', error);
      process.exit(1);
    });
}

module.exports = { clearDatabase };
