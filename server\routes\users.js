const express = require('express');
const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');
const { authenticateToken } = require('./auth');
const router = express.Router();

// الحصول على جميع المستخدمين (للمدراء فقط)
router.get('/', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لعرض المستخدمين'
      });
    }

    const [users] = await pool.execute(`
      SELECT u.id, u.name, u.email, u.role, u.phone, u.is_active, 
             u.created_at, u.updated_at, b.name as branch_name
      FROM users u
      LEFT JOIN branches b ON u.branch_id = b.id
      ORDER BY u.name
    `);

    res.json({
      success: true,
      data: { users }
    });

  } catch (error) {
    console.error('خطأ في الحصول على المستخدمين:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على مستخدم محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من صلاحيات الوصول
    if (req.user.role !== 'manager' && req.user.userId !== parseInt(id)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لعرض هذا المستخدم'
      });
    }

    const [users] = await pool.execute(`
      SELECT u.id, u.name, u.email, u.role, u.phone, u.is_active, 
             u.created_at, u.updated_at, b.name as branch_name, u.branch_id
      FROM users u
      LEFT JOIN branches b ON u.branch_id = b.id
      WHERE u.id = ?
    `, [id]);

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    res.json({
      success: true,
      data: { user: users[0] }
    });

  } catch (error) {
    console.error('خطأ في الحصول على المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء مستخدم جديد (للمدراء فقط)
router.post('/', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لإنشاء مستخدم جديد'
      });
    }

    const { name, email, password, role, branch_id, phone } = req.body;

    if (!name || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        message: 'الاسم والبريد الإلكتروني وكلمة المرور والدور مطلوبة'
      });
    }

    // التحقق من صحة الدور
    const validRoles = ['employee', 'supervisor', 'manager'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'الدور غير صالح'
      });
    }

    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد مستخدم بهذا البريد الإلكتروني بالفعل'
      });
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);

    // إنشاء المستخدم الجديد
    const [result] = await pool.execute(`
      INSERT INTO users (name, email, password, role, branch_id, phone, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [name, email, hashedPassword, role, branch_id || null, phone || null]);

    // الحصول على المستخدم المنشأ
    const [newUser] = await pool.execute(`
      SELECT u.id, u.name, u.email, u.role, u.phone, u.is_active, 
             u.created_at, u.updated_at, b.name as branch_name
      FROM users u
      LEFT JOIN branches b ON u.branch_id = b.id
      WHERE u.id = ?
    `, [result.insertId]);

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المستخدم بنجاح',
      data: { user: newUser[0] }
    });

  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تحديث مستخدم
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, role, branch_id, phone, is_active } = req.body;

    // التحقق من صلاحيات التحديث
    const isOwnProfile = req.user.userId === parseInt(id);
    const isManager = req.user.role === 'manager';

    if (!isOwnProfile && !isManager) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لتحديث هذا المستخدم'
      });
    }

    if (!name || !email) {
      return res.status(400).json({
        success: false,
        message: 'الاسم والبريد الإلكتروني مطلوبان'
      });
    }

    // التحقق من وجود المستخدم
    const [existingUser] = await pool.execute(
      'SELECT id, role FROM users WHERE id = ?',
      [id]
    );

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // التحقق من عدم وجود مستخدم آخر بنفس البريد الإلكتروني
    const [duplicateUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ? AND id != ?',
      [email, id]
    );

    if (duplicateUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد مستخدم آخر بهذا البريد الإلكتروني'
      });
    }

    // إعداد البيانات للتحديث
    let updateFields = ['name = ?', 'email = ?', 'updated_at = NOW()'];
    let updateValues = [name, email];

    // المدير فقط يمكنه تحديث الدور والفرع والحالة
    if (isManager) {
      if (role) {
        const validRoles = ['employee', 'supervisor', 'manager'];
        if (!validRoles.includes(role)) {
          return res.status(400).json({
            success: false,
            message: 'الدور غير صالح'
          });
        }
        updateFields.push('role = ?');
        updateValues.push(role);
      }

      if (branch_id !== undefined) {
        updateFields.push('branch_id = ?');
        updateValues.push(branch_id);
      }

      if (is_active !== undefined) {
        updateFields.push('is_active = ?');
        updateValues.push(is_active);
      }
    }

    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }

    updateValues.push(id);

    // تحديث المستخدم
    await pool.execute(`
      UPDATE users SET ${updateFields.join(', ')} WHERE id = ?
    `, updateValues);

    // الحصول على المستخدم المحدث
    const [updatedUser] = await pool.execute(`
      SELECT u.id, u.name, u.email, u.role, u.phone, u.is_active, 
             u.created_at, u.updated_at, b.name as branch_name
      FROM users u
      LEFT JOIN branches b ON u.branch_id = b.id
      WHERE u.id = ?
    `, [id]);

    res.json({
      success: true,
      message: 'تم تحديث المستخدم بنجاح',
      data: { user: updatedUser[0] }
    });

  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تغيير كلمة المرور
router.patch('/:id/password', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { current_password, new_password } = req.body;

    // التحقق من صلاحيات التحديث
    const isOwnProfile = req.user.userId === parseInt(id);
    const isManager = req.user.role === 'manager';

    if (!isOwnProfile && !isManager) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لتغيير كلمة مرور هذا المستخدم'
      });
    }

    if (!new_password) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الجديدة مطلوبة'
      });
    }

    if (new_password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
      });
    }

    // الحصول على المستخدم
    const [users] = await pool.execute(
      'SELECT password FROM users WHERE id = ?',
      [id]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // التحقق من كلمة المرور الحالية (للمستخدم نفسه فقط)
    if (isOwnProfile && !isManager) {
      if (!current_password) {
        return res.status(400).json({
          success: false,
          message: 'كلمة المرور الحالية مطلوبة'
        });
      }

      const isCurrentPasswordValid = await bcrypt.compare(current_password, users[0].password);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'كلمة المرور الحالية غير صحيحة'
        });
      }
    }

    // تشفير كلمة المرور الجديدة
    const hashedNewPassword = await bcrypt.hash(new_password, 10);

    // تحديث كلمة المرور
    await pool.execute(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedNewPassword, id]
    );

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تغيير كلمة المرور:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// حذف مستخدم (للمدراء فقط)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لحذف المستخدم'
      });
    }

    const { id } = req.params;

    // التحقق من عدم حذف المستخدم لنفسه
    if (req.user.userId === parseInt(id)) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكنك حذف حسابك الخاص'
      });
    }

    // التحقق من وجود المستخدم
    const [existingUser] = await pool.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // التحقق من عدم وجود تقارير مرتبطة بالمستخدم
    const [reports] = await pool.execute(
      'SELECT id FROM reports WHERE user_id = ?',
      [id]
    );

    if (reports.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن حذف المستخدم لوجود تقارير مرتبطة به'
      });
    }

    // حذف المستخدم (soft delete)
    await pool.execute(
      'UPDATE users SET is_active = false, updated_at = NOW() WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
