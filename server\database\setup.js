const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

const setupDatabase = async () => {
  let connection;

  try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      port: process.env.DB_PORT || 3306
    });

    console.log('🔗 تم الاتصال بـ MySQL');

    // إنشاء قاعدة البيانات
    await connection.execute('CREATE DATABASE IF NOT EXISTS sales_reports CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✅ تم إنشاء قاعدة البيانات');

    // الاتصال بقاعدة البيانات المحددة
    await connection.changeUser({ database: 'sales_reports' });
    console.log('🔗 تم الاتصال بقاعدة البيانات sales_reports');

    // إنشاء الجداول
    const tables = [
      `CREATE TABLE IF NOT EXISTS branches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255),
        phone VARCHAR(50),
        email VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('Employee', 'Supervisor', 'Manager') DEFAULT 'Employee',
        branch_id INT,
        phone VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL
      )`,

      `CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        model_name VARCHAR(255) NOT NULL,
        brand VARCHAR(100),
        category VARCHAR(100),
        description TEXT,
        unit_price DECIMAL(10, 2),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      `CREATE TABLE IF NOT EXISTS reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        report_date DATE NOT NULL,
        notes TEXT,
        total_amount DECIMAL(12, 2) DEFAULT 0,
        status ENUM('draft', 'submitted', 'approved', 'rejected') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )`,

      `CREATE TABLE IF NOT EXISTS report_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 0,
        unit_price DECIMAL(10, 2) NOT NULL DEFAULT 0,
        total_price DECIMAL(12, 2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
      )`
    ];

    // تنفيذ إنشاء الجداول
    for (const tableSQL of tables) {
      await connection.execute(tableSQL);
    }
    console.log('✅ تم إنشاء الجداول بنجاح');

    // إدراج البيانات التجريبية
    await insertSampleData(connection);

    // التحقق من الجداول المنشأة
    const [tablesList] = await connection.execute('SHOW TABLES');
    console.log('📋 الجداول المنشأة:');
    tablesList.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 تم إغلاق الاتصال');
    }
  }
};

const insertSampleData = async (connection) => {
  try {
    // إدراج الفروع
    await connection.execute(`
      INSERT IGNORE INTO branches (name, location, phone, email) VALUES
      ('الفرع الرئيسي', 'المنامة، البحرين', '+973-1234-5678', '<EMAIL>'),
      ('فرع الشمال', 'المحرق، البحرين', '+973-1234-5679', '<EMAIL>'),
      ('فرع الجنوب', 'الرفاع، البحرين', '+973-1234-5680', '<EMAIL>')
    `);

    // إدراج المستخدمين
    await connection.execute(`
      INSERT IGNORE INTO users (name, email, password, role, branch_id, phone) VALUES
      ('أحمد محمد', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Manager', 1, '+973-3333-1111'),
      ('فاطمة أحمد', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Supervisor', 2, '+973-3333-2222'),
      ('محمد علي', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Employee', 1, '+973-3333-3333'),
      ('سارة خالد', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Employee', 3, '+973-3333-4444')
    `);

    // إدراج المنتجات
    await connection.execute(`
      INSERT IGNORE INTO products (model_name, brand, category, unit_price) VALUES
      ('لابتوب ديل XPS 13', 'Dell', 'أجهزة كمبيوتر', 450.00),
      ('لابتوب HP Pavilion', 'HP', 'أجهزة كمبيوتر', 350.00),
      ('ماوس لوجيتك MX Master', 'Logitech', 'ملحقات', 25.00),
      ('كيبورد ميكانيكي', 'Corsair', 'ملحقات', 40.00),
      ('شاشة سامسونج 24 بوصة', 'Samsung', 'شاشات', 120.00),
      ('طابعة كانون', 'Canon', 'طابعات', 80.00),
      ('سماعات سوني', 'Sony', 'صوتيات', 60.00),
      ('كاميرا ويب', 'Logitech', 'ملحقات', 30.00)
    `);

    console.log('✅ تم إدراج البيانات التجريبية بنجاح');
  } catch (error) {
    console.error('❌ خطأ في إدراج البيانات التجريبية:', error.message);
  }
};

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  setupDatabase()
    .then(() => {
      console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في إعداد قاعدة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { setupDatabase };
