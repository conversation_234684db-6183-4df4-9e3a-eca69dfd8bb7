const express = require('express');
const { pool } = require('../config/database');
const { authenticateToken } = require('./auth');
const router = express.Router();

// الحصول على جميع الفروع
router.get('/', authenticateToken, async (req, res) => {
  try {
    const [branches] = await pool.execute(
      'SELECT * FROM branches ORDER BY name'
    );

    res.json({
      success: true,
      data: { branches }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الفروع:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// الحصول على فرع محدد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const [branches] = await pool.execute(
      'SELECT * FROM branches WHERE id = ?',
      [id]
    );

    if (branches.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الفرع غير موجود'
      });
    }

    res.json({
      success: true,
      data: { branch: branches[0] }
    });

  } catch (error) {
    console.error('خطأ في الحصول على الفرع:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// إنشاء فرع جديد (للمدراء فقط)
router.post('/', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لإنشاء فرع جديد'
      });
    }

    const { name, address, phone, manager_name } = req.body;

    if (!name || !address) {
      return res.status(400).json({
        success: false,
        message: 'اسم الفرع والعنوان مطلوبان'
      });
    }

    // التحقق من عدم وجود فرع بنفس الاسم
    const [existingBranches] = await pool.execute(
      'SELECT id FROM branches WHERE name = ?',
      [name]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد فرع بهذا الاسم بالفعل'
      });
    }

    // إنشاء الفرع الجديد
    const [result] = await pool.execute(
      `INSERT INTO branches (name, address, phone, manager_name, created_at, updated_at) 
       VALUES (?, ?, ?, ?, NOW(), NOW())`,
      [name, address, phone || null, manager_name || null]
    );

    // الحصول على الفرع المنشأ
    const [newBranch] = await pool.execute(
      'SELECT * FROM branches WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الفرع بنجاح',
      data: { branch: newBranch[0] }
    });

  } catch (error) {
    console.error('خطأ في إنشاء الفرع:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// تحديث فرع (للمدراء فقط)
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لتحديث الفرع'
      });
    }

    const { id } = req.params;
    const { name, address, phone, manager_name } = req.body;

    if (!name || !address) {
      return res.status(400).json({
        success: false,
        message: 'اسم الفرع والعنوان مطلوبان'
      });
    }

    // التحقق من وجود الفرع
    const [existingBranch] = await pool.execute(
      'SELECT id FROM branches WHERE id = ?',
      [id]
    );

    if (existingBranch.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الفرع غير موجود'
      });
    }

    // التحقق من عدم وجود فرع آخر بنفس الاسم
    const [duplicateBranches] = await pool.execute(
      'SELECT id FROM branches WHERE name = ? AND id != ?',
      [name, id]
    );

    if (duplicateBranches.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد فرع آخر بهذا الاسم'
      });
    }

    // تحديث الفرع
    await pool.execute(
      `UPDATE branches 
       SET name = ?, address = ?, phone = ?, manager_name = ?, updated_at = NOW() 
       WHERE id = ?`,
      [name, address, phone || null, manager_name || null, id]
    );

    // الحصول على الفرع المحدث
    const [updatedBranch] = await pool.execute(
      'SELECT * FROM branches WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: 'تم تحديث الفرع بنجاح',
      data: { branch: updatedBranch[0] }
    });

  } catch (error) {
    console.error('خطأ في تحديث الفرع:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

// حذف فرع (للمدراء فقط)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    // التحقق من صلاحيات المستخدم
    if (req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لحذف الفرع'
      });
    }

    const { id } = req.params;

    // التحقق من وجود الفرع
    const [existingBranch] = await pool.execute(
      'SELECT id FROM branches WHERE id = ?',
      [id]
    );

    if (existingBranch.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الفرع غير موجود'
      });
    }

    // التحقق من عدم وجود مستخدمين مرتبطين بالفرع
    const [users] = await pool.execute(
      'SELECT id FROM users WHERE branch_id = ?',
      [id]
    );

    if (users.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن حذف الفرع لوجود مستخدمين مرتبطين به'
      });
    }

    // حذف الفرع (soft delete)
    await pool.execute(
      'UPDATE branches SET is_active = false, updated_at = NOW() WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: 'تم حذف الفرع بنجاح'
    });

  } catch (error) {
    console.error('خطأ في حذف الفرع:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
});

module.exports = router;
