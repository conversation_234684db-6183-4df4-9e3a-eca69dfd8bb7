-- تشغيل هذا الملف في MySQL Workbench
-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS sales_reports CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sales_reports;
-- جدول الفروع
CREATE TABLE IF NOT EXISTS branches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('Employee', 'Supervisor', 'Manager') DEFAULT 'Employee',
    branch_id INT,
    phone VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE
    SET NULL
);
-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    model_name VARCHAR(255) NOT NULL,
    brand VARCHAR(100),
    category VARCHAR(100),
    description TEXT,
    unit_price DECIMAL(10, 2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- جدول التقارير
CREATE TABLE IF NOT EXISTS reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    report_date DATE NOT NULL,
    notes TEXT,
    total_amount DECIMAL(12, 2) DEFAULT 0,
    status ENUM('draft', 'submitted', 'approved', 'rejected') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
-- جدول عناصر التقارير
CREATE TABLE IF NOT EXISTS report_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 0,
    unit_price DECIMAL(10, 2) NOT NULL DEFAULT 0,
    total_price DECIMAL(12, 2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
-- إدراج بيانات تجريبية للفروع
INSERT INTO branches (name, location, phone, email)
VALUES (
        'الفرع الرئيسي',
        'المنامة، البحرين',
        '+973-1234-5678',
        '<EMAIL>'
    ),
    (
        'فرع الشمال',
        'المحرق، البحرين',
        '+973-1234-5679',
        '<EMAIL>'
    ),
    (
        'فرع الجنوب',
        'الرفاع، البحرين',
        '+973-1234-5680',
        '<EMAIL>'
    );
-- إدراج بيانات تجريبية للمستخدمين (كلمة المرور: password123)
INSERT INTO users (name, email, password, role, branch_id, phone)
VALUES (
        'أحمد محمد',
        '<EMAIL>',
        '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        'Manager',
        1,
        '+973-3333-1111'
    ),
    (
        'فاطمة أحمد',
        '<EMAIL>',
        '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        'Supervisor',
        2,
        '+973-3333-2222'
    ),
    (
        'محمد علي',
        '<EMAIL>',
        '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        'Employee',
        1,
        '+973-3333-3333'
    ),
    (
        'سارة خالد',
        '<EMAIL>',
        '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        'Employee',
        3,
        '+973-3333-4444'
    );
-- إدراج بيانات تجريبية للمنتجات
INSERT INTO products (model_name, brand, category, unit_price)
VALUES (
        'لابتوب ديل XPS 13',
        'Dell',
        'أجهزة كمبيوتر',
        450.00
    ),
    (
        'لابتوب HP Pavilion',
        'HP',
        'أجهزة كمبيوتر',
        350.00
    ),
    (
        'ماوس لوجيتك MX Master',
        'Logitech',
        'ملحقات',
        25.00
    ),
    ('كيبورد ميكانيكي', 'Corsair', 'ملحقات', 40.00),
    (
        'شاشة سامسونج 24 بوصة',
        'Samsung',
        'شاشات',
        120.00
    ),
    ('طابعة كانون', 'Canon', 'طابعات', 80.00),
    ('سماعات سوني', 'Sony', 'صوتيات', 60.00),
    ('كاميرا ويب', 'Logitech', 'ملحقات', 30.00);
-- التحقق من الجداول المنشأة
SHOW TABLES;
-- عرض بعض البيانات للتأكد
SELECT 'الفروع' as table_name,
    COUNT(*) as count
FROM branches
UNION ALL
SELECT 'المستخدمين',
    COUNT(*)
FROM users
UNION ALL
SELECT 'المنتجات',
    COUNT(*)
FROM products;