{"name": "daily-reports-server", "version": "1.0.0", "description": "Backend API for Daily Reports App", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mysql", "api", "reports"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5"}, "devDependencies": {"nodemon": "^3.0.2"}}