const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { testConnection } = require('./config/database');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في API تطبيق التقارير اليومية',
    status: 'يعمل بنجاح',
    version: '1.0.0'
  });
});

// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
  const isConnected = await testConnection();
  if (isConnected) {
    res.json({
      success: true,
      message: 'الاتصال بقاعدة البيانات يعمل بنجاح'
    });
  } else {
    res.status(500).json({
      success: false,
      message: 'فشل في الاتصال بقاعدة البيانات'
    });
  }
});

// Import routes
const { router: authRoutes } = require('./routes/auth');
const branchesRoutes = require('./routes/branches');
const productsRoutes = require('./routes/products');
const reportsRoutes = require('./routes/reports');
const usersRoutes = require('./routes/users');
const statsRoutes = require('./routes/stats');

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/branches', branchesRoutes);
app.use('/api/products', productsRoutes);
app.use('/api/reports', reportsRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/stats', statsRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    await testConnection();

    app.listen(PORT, () => {
      console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
      console.log(`🌐 الرابط: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('❌ فشل في بدء تشغيل الخادم:', error);
    process.exit(1);
  }
};

startServer();
